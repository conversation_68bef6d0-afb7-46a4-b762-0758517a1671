import flet as ft

import asyncio
import random
from a1111client import A1111Client, BasePayload


class GenerationTaskTest:



    async def remove_from_parent(self):
        self.working
        await asyncio.sleep(self.duration / 1000)  # Convert milliseconds to seconds
        print(f"Removing task: {self.label}")
        if self.content in self.parent_container.controls:
            self.parent_container.controls.remove(self.content)
            self.page.update()
        else:
            print(f"Content not found in parent container: {self.label}")

    def __init__(self, page: ft.Page, parent_container, label, duration):
        self.page = page
        self.parent_container = parent_container
        self.label = label
        self.duration = duration
        self.content = self.build()
        self.working = False
        # asyncio.create_task(self.remove_from_parent())


    async def progress_handler(self):
        while True:
            if not self.working:
                break
            await asyncio.sleep(0.5)
            self.content.value = self.content.value + 0.1
            if self.content.value >= 1:
                self.page.run_task(self.remove_from_parent)
            self.page.update()


    async def start(self):
        self.working = True
        self.content = ft.ProgressRing(value=0)
        self.parent_container.controls[-1] = self.content
        self.page.run_task(self.progress_handler)
        self.page.update()



    def cancel(self, e):
        self.working = False
        if self.content in self.parent_container.controls:
            self.parent_container.controls.remove(self.content)
            self.page.update()

    def build(self):
        self.cancel_button = ft.IconButton(icon=ft.Icons.CANCEL, on_click=self.cancel)
        return ft.Row(controls=[ft.Text(self.label), self.cancel_button])


class A1111ClientApp:
    def __init__(self,):
        self.tabs = [
            {
                "title": "Home",
                "icon": ft.Icons.HOME,
                "content": self.generate_home_content,
            },
            {
                "title": "Images",
                "icon": ft.Icons.IMAGE,
                "content": self.generate_images_content,
            },
            {
                "title": "Settings",
                "icon": ft.Icons.SETTINGS,
                "content": self.generate_settings_content,
            },
        ]

    def generate_home_content(self):
        return self.main_column

    def generate_images_content(self):
        return ft.Text("Images")

    def generate_settings_content(self):
        return ft.Text("Settings")

    def generate_tab(self, tab_index):
        return self.tabs[tab_index]["content"]()

    
    async def try_save_value(self, key, value):
        await self.page.client_storage.set_async(key, value)
    
    async def on_navigation_bar_change(self, e):
        self.main_container.content = self.generate_tab(int(e.data))
        await self.try_save_value("selected_index", int(e.data))
        self.page.update()



    async def try_load_value(self, key, default_value = None):
        value = await self.page.client_storage.get_async(key)
        if value is None:
            await self.page.client_storage.set_async(key, default_value)
            return default_value
        await self.page.client_storage.set_async(key, value)
        return value
    
    async def set_async_task(self, label, duration):
        task = GenerationTaskTest(self.page, self.main_column, label, duration)
        self.main_column.controls.append(task.content)
        self.page.update()
        self.page.run_task(task.start)
        # self.page.run_task(task.remove_from_parent)

    async def floating_action(self, e):
        await self.set_async_task("Task added", 2000)

    async def main(self, page: ft.Page):
        self.page = page
        page.title = "A1111 Client"
        self.selected_index = await self.try_load_value("selected_index", 0)
        print(self.selected_index)
        self.navigation_bar = ft.NavigationBar(
            selected_index=self.selected_index,
            destinations=[
                ft.NavigationBarDestination(icon=ft.Icons.HOME, label="Home"),
                ft.NavigationBarDestination(icon=ft.Icons.IMAGE, label="Images"),
                ft.NavigationBarDestination(icon=ft.Icons.SETTINGS, label="Settings"),
            ],
            on_change=self.on_navigation_bar_change,
        )
        self.main_column = ft.Column([ft.Text("hola")], expand=True)
        self.main_container = ft.Container(self.main_column, expand=True) 
        self.main_area = ft.SafeArea(self.main_container, expand=True)
        self.floating_button = ft.FloatingActionButton(icon=ft.Icons.ADD, on_click=self.floating_action)
        self.page.floating_action_button = self.floating_button
        page.navigation_bar = self.navigation_bar
        page.add(self.main_area)
        self.main_container.content = self.generate_tab(self.selected_index)
        page.update()
    

ft.app(target=A1111ClientApp().main)