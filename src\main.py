import flet as ft



class A1111ClientApp:
    def __init__(self):
        self.tabs = [
            {
                "title": "Home",
                "icon": ft.Icons.HOME,
                "content": self.generate_home_content,
            },
            {
                "title": "Images",
                "icon": ft.Icons.IMAGE,
                "content": self.generate_images_content,
            },
            {
                "title": "Settings",
                "icon": ft.Icons.SETTINGS,
                "content": self.generate_settings_content,
            },
        ]

    def generate_home_content(self):
        return ft.Text("Home")

    def generate_images_content(self):
        return ft.Text("Images")

    def generate_settings_content(self):
        return ft.Text("Settings")

    def generate_tab(self, tab_index):
        return self.tabs[tab_index]["content"]()

    
    
    
    def on_navigation_bar_change(self, e):
        self.main_container.content = self.generate_tab(int(e.data))
        self.page.update()



    async def try_load_value(self, key, default_value = None):
        value = await self.page.client_storage.get_async(key)
        if value is None:
            await self.page.client_storage.set_async(key, default_value)
            return default_value
        await self.page.client_storage.set_async(key, value)
        return value

    async def main(self, page: ft.Page):
        self.page = page
        page.title = "A1111 Client"
        self.selected_index = await self.try_load_value("selected_index", 0)
        print(self.selected_index)
        self.navigation_bar = ft.NavigationBar(
            selected_index=0,
            destinations=[
                ft.NavigationBarDestination(icon=ft.Icons.HOME, label="Home"),
                ft.NavigationBarDestination(icon=ft.Icons.IMAGE, label="Images"),
                ft.NavigationBarDestination(icon=ft.Icons.SETTINGS, label="Settings"),
            ],
            on_change=self.on_navigation_bar_change,
        )
        self.main_container = ft.Container(expand=True) 
        self.main_area = ft.SafeArea(self.main_container, expand=True)
        page.navigation_bar = self.navigation_bar
        page.add(self.main_area)
        self.main_container.content = self.generate_tab(self.selected_index)
        page.update()
    

ft.app(target=A1111ClientApp().main)