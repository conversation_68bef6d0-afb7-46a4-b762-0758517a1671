import flet as ft
import asyncio
import random
from a1111client import A1111Client, BasePayload
import aiohttp


test_url = "https://rncrj-3-135-152-169.a.free.pinggy.link/"

class TaskQueue:
    def __init__(self):
        self.queue = []
        self.current_task = None
        self.is_processing = False
    
    def add_task(self, task):
        self.queue.append(task)
        task.queue_position = len(self.queue)
        task.update_status()
    
    def remove_task(self, task):
        if task in self.queue:
            self.queue.remove(task)
            # Actualizar posiciones de las tareas restantes
            for i, t in enumerate(self.queue):
                t.queue_position = i + 1
                t.update_status()
    
    def get_next_task(self):
        if self.queue:
            return self.queue.pop(0)
        return None
    
    async def process_queue(self):
        while True:
            if not self.is_processing and self.queue:
                self.is_processing = True
                self.current_task = self.get_next_task()
                
                # Actualizar posiciones de las tareas restantes
                for i, task in enumerate(self.queue):
                    task.queue_position = i + 1
                    task.update_status()
                
                await self.current_task.start()
                self.current_task = None
                self.is_processing = False
            
            await asyncio.sleep(0.1)


class GenerationTaskTest:
    def __init__(self, page: ft.Page, parent_container, label, duration, task_queue):
        self.page = page
        self.parent_container = parent_container
        self.label = label
        self.duration = duration
        self.task_queue = task_queue
        self.working = False
        self.queue_position = 0
        self.content = self.build()
        self.progress_ring = None
        print(self.duration)
        
    def update_status(self):
        if self.queue_position == 1:
            self.status_text.value = "Próximo en cola"
            self.cancel_button.visible = True  # Permitir cancelar el próximo en cola
        elif self.queue_position > 1:
            self.status_text.value = f"En cola (#{self.queue_position})"
            self.cancel_button.visible = True
        else:
            self.status_text.value = "Procesando..."
            self.cancel_button.visible = False  # Solo ocultar para la tarea activa

        self.page.update()

    async def remove_from_parent(self):
        print(f"Removing task: {self.label}")
        self.working = False
        if self.content in self.parent_container.controls:
            self.parent_container.controls.remove(self.content)
            self.page.update()
            print(f"Successfully removed: {self.label}")
        else:
            print(f"Content not found in parent container: {self.label}")

    async def start_handler(self):

        # we now start the image generation with aiohttp to the test_url

        # we create a payload
        payload = BasePayload(
            prompt="A professional photograph of an astronaut riding a horse",
            negative_prompt="blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            steps=20,
            cfg_scale=7,
            width=512,
            height=512
        )

        # we create the client
        client = A1111Client(test_url)

        # we generate the image
        result = await client.generate_image(payload)

        # we download the image
        image = await client.download_image(result["images"][0])

        # we save the image
        image.save(f"output/{self.label}.png")
        
        # Completar tarea
        if self.working:  # Solo completar si no fue cancelada
            await self.complete_task()

    async def complete_task(self):
        """Completar la tarea y notificar al queue"""
        print(f"Completing task: {self.label}")
        self.working = False

        # Remover de la UI
        if self.content in self.parent_container.controls:
            self.parent_container.controls.remove(self.content)

        # Notificar al queue que la tarea terminó
        # self.task_queue.task_completed(self)
        self.page.update()

    async def start(self):
        self.working = True
        self.queue_position = 0

        # Cambiar a anillo de progreso
        self.progress_ring = ft.ProgressRing(value=0)
        self.content.content = ft.Row(
            controls=[
                ft.Column(
                    controls=[
                        ft.Text(self.label, weight=ft.FontWeight.BOLD),
                        ft.Text("Procesando...", size=12, color=ft.Colors.BLUE)
                    ],
                    spacing=2
                ),
                self.progress_ring
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
        )
        self.page.update()

        # Iniciar progreso en background
        await self.start_handler()

    def cancel(self, e):
        if self.queue_position > 0:  # Cancelar cualquier tarea que no esté procesando (posición 0)
            self.task_queue.remove_task(self)
            if self.content in self.parent_container.controls:
                self.parent_container.controls.remove(self.content)
                self.page.update()
                print(f"Cancelled task: {self.label}")

    def build(self):
        self.cancel_button = ft.IconButton(
            icon=ft.Icons.CANCEL, 
            on_click=self.cancel,
            visible=False,
            tooltip="Cancelar tarea"
        )
        self.status_text = ft.Text("En cola", size=12, color=ft.Colors.GREY)
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Column(
                        controls=[
                            ft.Text(self.label, weight=ft.FontWeight.BOLD),
                            self.status_text
                        ],
                        spacing=2
                    ),
                    self.cancel_button
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
            ),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            margin=ft.margin.only(bottom=5)
        )


class A1111ClientApp:
    def __init__(self,):
        self.task_queue = TaskQueue()
        self.tabs = [
            {
                "title": "Home",
                "icon": ft.Icons.HOME,
                "content": self.generate_home_content,
            },
            {
                "title": "Images",
                "icon": ft.Icons.IMAGE,
                "content": self.generate_images_content,
            },
            {
                "title": "Settings",
                "icon": ft.Icons.SETTINGS,
                "content": self.generate_settings_content,
            },
        ]

    def generate_home_content(self):
        return self.main_column

    def generate_images_content(self):
        return ft.Text("Images")

    def generate_settings_content(self):
        return ft.Text("Settings")

    def generate_tab(self, tab_index):
        return self.tabs[tab_index]["content"]()

    async def try_save_value(self, key, value):
        await self.page.client_storage.set_async(key, value)
    
    async def on_navigation_bar_change(self, e):
        self.main_container.content = self.generate_tab(int(e.data))
        await self.try_save_value("selected_index", int(e.data))
        self.page.update()

    async def try_load_value(self, key, default_value = None):
        value = await self.page.client_storage.get_async(key)
        if value is None:
            await self.page.client_storage.set_async(key, default_value)
            return default_value
        await self.page.client_storage.set_async(key, value)
        return value
    
    async def set_async_task(self, label, duration):
        task = GenerationTaskTest(self.page, self.main_column, label, duration, self.task_queue)
        self.main_column.controls.append(task.content)
        self.task_queue.add_task(task)
        self.page.update()

    async def floating_action(self, e):
        task_count = len(self.task_queue.queue) + (1 if self.task_queue.current_task else 0)
        await self.set_async_task(f"Tarea #{task_count + 1}", random.randint(1, 5))

    async def main(self, page: ft.Page):
        self.page = page
        page.title = "A1111 Client"
        self.selected_index = await self.try_load_value("selected_index", 0)
        print(self.selected_index)
        
        self.navigation_bar = ft.NavigationBar(
            selected_index=self.selected_index,
            destinations=[
                ft.NavigationBarDestination(icon=ft.Icons.HOME, label="Home"),
                ft.NavigationBarDestination(icon=ft.Icons.IMAGE, label="Images"),
                ft.NavigationBarDestination(icon=ft.Icons.SETTINGS, label="Settings"),
            ],
            on_change=self.on_navigation_bar_change,
        )
        
        self.main_column = ft.Column([
            ft.Text("Cola de Tareas A1111", size=24, weight=ft.FontWeight.BOLD),
            ft.Divider()
        ], expand=True, scroll=ft.ScrollMode.AUTO)
        
        self.main_container = ft.Container(self.main_column, expand=True) 
        self.main_area = ft.SafeArea(self.main_container, expand=True)
        self.floating_button = ft.FloatingActionButton(
            icon=ft.Icons.ADD, 
            on_click=self.floating_action,
            tooltip="Agregar tarea a la cola"
        )
        
        self.page.floating_action_button = self.floating_button
        page.navigation_bar = self.navigation_bar
        page.add(self.main_area)
        self.main_container.content = self.generate_tab(self.selected_index)
        
        # Iniciar el procesador de cola
        self.page.run_task(self.task_queue.process_queue)
        
        page.update()

ft.app(target=A1111ClientApp().main)