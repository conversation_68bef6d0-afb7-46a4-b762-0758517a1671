import flet as ft



class A1111ClientApp:
    def __init__(self):
        self.tabs = [
            {
                "title": "Home",
                "icon": ft.Icons.HOME,
                "content": self.generate_home_content,
            },
            {
                "title": "Images",
                "icon": ft.Icons.IMAGE,
                "content": self.generate_images_content,
            },
            {
                "title": "Settings",
                "icon": ft.Icons.SETTINGS,
                "content": self.generate_settings_content,
            },
        ]

    def generate_home_content(self):
        return ft.Text("Home")

    def generate_images_content(self):
        return ft.Text("Images")

    def generate_settings_content(self):
        return ft.Text("Settings")

    def generate_tab(self, tab_index):
        return self.tabs[tab_index]["content"]()

    
    async def try_save_value(self, key, value):
        await self.page.client_storage.set_async(key, value)
    
    async def on_navigation_bar_change(self, e):
        self.main_container.content = self.generate_tab(int(e.data))
        await self.try_save_value("selected_index", int(e.data))
        self.page.update()



    async def try_load_value(self, key, default_value = None):
        value = await self.page.client_storage.get_async(key)
        if value is None:
            await self.page.client_storage.set_async(key, default_value)
            return default_value
        await self.page.client_storage.set_async(key, value)
        return value
    
    async def set_async_task(self, label, duration):
        self.main_column.controls.append(ft.Text(label))
        self.page.update()

    async def floating_action(self, e):
        await self.set_async_task("Task added", 2000)

    async def main(self, page: ft.Page):
        self.page = page
        page.title = "A1111 Client"
        self.selected_index = await self.try_load_value("selected_index", 0)
        print(self.selected_index)
        self.navigation_bar = ft.NavigationBar(
            selected_index=self.selected_index,
            destinations=[
                ft.NavigationBarDestination(icon=ft.Icons.HOME, label="Home"),
                ft.NavigationBarDestination(icon=ft.Icons.IMAGE, label="Images"),
                ft.NavigationBarDestination(icon=ft.Icons.SETTINGS, label="Settings"),
            ],
            on_change=self.on_navigation_bar_change,
        )
        self.main_column = ft.Column(expand=True)
        self.main_container = ft.Container(self.main_column, expand=True) 
        self.main_area = ft.SafeArea(self.main_container, expand=True)
        self.floating_button = ft.FloatingActionButton(icon=ft.Icons.ADD, on_click=floating_action)
        self.page.floating_action_button = self.floating_button    
        page.navigation_bar = self.navigation_bar
        page.add(self.main_area)
        self.main_container.content = self.generate_tab(self.selected_index)
        page.update()
    

ft.app(target=A1111ClientApp().main)