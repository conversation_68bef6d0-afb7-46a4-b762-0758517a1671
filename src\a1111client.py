import asyncio
import aiohttp

# we will use pillow
from PIL import Image
import io
import base64
import random
import string


class BasePayload:
    models_settings = [
        {
            "juggernaut_xl": {
                "width": 1024,
                "height": 1024,
                "steps": 20,
                "cfg_scale": 7,
                "negative_prompt": "blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            }
        }
    ]

    def __init__(
        self,
        prompt="",
        negative_prompt="",
        steps=20,
        cfg_scale=7,
        width=512,
        height=512,
        sampler_name="Euler a",
        scheduler="Normal",
        n_iter=1,
        **kwargs
    ):
        # Parámetros básicos requeridos
        self.prompt = prompt
        self.negative_prompt = negative_prompt
        self.steps = steps
        self.cfg_scale = cfg_scale
        self.width = width
        self.height = height
        self.sampler_name = sampler_name
        self.scheduler = scheduler
        self.n_iter = n_iter

        # Agregar cualquier parámetro adicional dinámicamente
        for key, value in kwargs.items():
            setattr(self, key, value)

    @classmethod
    def from_dict(cls, data_dict):
        """Crear BasePayload desde un diccionario"""
        return cls(**data_dict)

    def to_dict(self):
        """Convertir BasePayload a diccionario"""
        return {key: value for key, value in self.__dict__.items() if not key.startswith('_')}

    def copy_with_updates(self, **updates):
        """Crear una copia del payload con actualizaciones"""
        current_dict = self.to_dict()
        current_dict.update(updates)
        return BasePayload.from_dict(current_dict)

    def payload_from_model(self, model_name):
        for model in self.models_settings:
            if model_name in model:
                return model[model_name]
        return None


class A1111Client:
    def __init__(self, url):
        self.url = url
        self.headers = {
            "Content-Type": "application/json",
        }
        self.session = aiohttp.ClientSession(headers=self.headers)

    async def generate_image(self, payload: BasePayload):
        data = {}
        for key, value in payload.__dict__.items():
            data[key] = value

        async with self.session.post(
            f"{self.url}/sdapi/v1/txt2img", json=data
        ) as response:
            result = await response.json()
            return result

    async def download_image(self, image):
        async with self.session.get(image) as response:
            image_data = await response.read()
            image = Image.open(io.BytesIO(image_data))
            return image
