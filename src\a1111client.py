﻿import asyncio
import aiohttp
# we will use pillow
from PIL import Image
import io
import base64
import random
import string

class BasePayload:

    models_settings = [
        {
            "juggernaut_xl": {
                "width": 1024,
                "height": 1024,
                "steps": 20,
                "cfg_scale": 7,
                "negative_prompt": "blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            }
        }
    ]

    def __init__(self, prompt, negative_prompt, steps, cfg_scale, width, height):
        self.prompt = prompt
        self.negative_prompt = negative_prompt
        self.steps = steps
        self.cfg_scale = cfg_scale
        self.width = width
        self.height = height

    def payload_from_model(self, model_name):
        for model in self.models_settings:
            if model_name in model:
                return model[model_name]
        return None


class A1111Client:
    def __init__(self, url, username, password):
        self.url = url
        self.username = username
        self.password = password
        self.headers = {
            "Content-Type": "application/json",
        }
        self.session = aiohttp.ClientSession(
            headers=self.headers, auth=aiohttp.BasicAuth(self.username, self.password)
        )

    async def generate_image(self, payload: BasePayload):
        
        data = {
            "prompt": payload.prompt,
            "negative_prompt": payload.negative_prompt,
            "steps": payload.steps,
            "cfg_scale": payload.cfg_scale,
            "width": payload.width,
            "height": payload.height,
        }
        async with self.session.post(
            f"{self.url}/sdapi/v1/txt2img", json=data
        ) as response:
            result = await response.json()
            return result

    async def download_image(self, image):
        async with self.session.get(image) as response:
            image_data = await response.read()
            image = Image.open(io.BytesIO(image_data))
            return image