import asyncio
import aiohttp

# we will use pillow
from PIL import Image
import io
import base64
import random
import string


class BasePayload:
    models_settings = [
        {
            "juggernaut_xl": {
                "width": 1024,
                "height": 1024,
                "steps": 20,
                "cfg_scale": 7,
                "negative_prompt": "blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            }
        }
    ]

    def __init__(
        self,
        prompt="",
        negative_prompt="",
        steps=20,
        cfg_scale=7,
        width=512,
        height=512,
        sampler_name="Euler a",
        scheduler="Normal",
        n_iter=1,
        **kwargs
    ):
        # Parámetros básicos requeridos
        self.prompt = prompt
        self.negative_prompt = negative_prompt
        self.steps = steps
        self.cfg_scale = cfg_scale
        self.width = width
        self.height = height
        self.sampler_name = sampler_name
        self.scheduler = scheduler
        self.n_iter = n_iter

        # Agregar cualquier parámetro adicional dinámicamente
        for key, value in kwargs.items():
            setattr(self, key, value)

    @classmethod
    def from_dict(cls, data_dict):
        """Crear BasePayload desde un diccionario"""
        return cls(**data_dict)

    def to_dict(self):
        """Convertir BasePayload a diccionario"""
        return {key: value for key, value in self.__dict__.items() if not key.startswith('_')}

    def copy_with_updates(self, **updates):
        """Crear una copia del payload con actualizaciones"""
        current_dict = self.to_dict()
        current_dict.update(updates)
        return BasePayload.from_dict(current_dict)

    def payload_from_model(self, model_name):
        for model in self.models_settings:
            if model_name in model:
                return model[model_name]
        return None


class A1111Client:
    def __init__(self, url):
        self.url = url
        self.headers = {
            "Content-Type": "application/json",
        }
        # Don't create session in __init__ to avoid leaks

    async def generate_image(self, payload: BasePayload, endpoint_type="txt2img"):
        """Generate image using specified endpoint type"""
        data = {}
        for key, value in payload.__dict__.items():
            data[key] = value

        # Determine the correct endpoint
        endpoint_map = {
            "txt2img": "/sdapi/v1/txt2img",
            "img2img": "/sdapi/v1/img2img",
            "upscale": "/sdapi/v1/extra-single-image",
            "hr_upscale": "/sdapi/v1/txt2img",  # HR upscale uses txt2img with HR params
            "repeat": "/sdapi/v1/txt2img"  # Repeat also uses txt2img endpoint
        }

        endpoint = endpoint_map.get(endpoint_type, "/sdapi/v1/txt2img")

        # For upscale endpoint, format data differently
        if endpoint_type == "upscale":
            data = self._format_upscale_data(data)

        # Use session context manager to ensure proper cleanup
        async with aiohttp.ClientSession(headers=self.headers) as session:
            async with session.post(
                f"{self.url}{endpoint}", json=data
            ) as response:
                result = await response.json()
                return result

    def _format_upscale_data(self, data):
        """Format data for upscale endpoint"""
        # The extra-single-image endpoint expects different parameters
        upscale_data = {
            "resize_mode": 0,
            "show_extras_results": True,
            "gfpgan_visibility": 0,
            "codeformer_visibility": 0,
            "codeformer_weight": 0,
            "upscaling_resize": data.get("hr_scale", 2.0),
            "upscaling_resize_w": data.get("width", 512) * data.get("hr_scale", 2.0),
            "upscaling_resize_h": data.get("height", 512) * data.get("hr_scale", 2.0),
            "upscaling_crop": True,
            "upscaler_1": data.get("hr_upscaler", "R-ESRGAN 4x+"),
            "upscaler_2": "None",
            "extras_upscaler_2_visibility": 0,
            "upscale_first": False,
        }

        # If we have an image to upscale, add it
        if "image" in data:
            upscale_data["image"] = data["image"]

        return upscale_data

    async def download_image(self, image):
        async with aiohttp.ClientSession(headers=self.headers) as session:
            async with session.get(image) as response:
                image_data = await response.read()
                image = Image.open(io.BytesIO(image_data))
                return image
