from a1111generation_task_queue import TaskQueue
from a1111generation_task import GenerationTaskTest
from a1111prompt_panel import PromptPanel
import flet as ft
import os
from a1111image_viewer import ImageViewer

app_data_path = os.getenv("FLET_APP_STORAGE_DATA")
app_temp_path = os.getenv("FLET_APP_STORAGE_TEMP")

remove_delay = 0

class A1111ClientApp:
    def __init__(
        self,
    ):
        self.task_queue = TaskQueue()
        self.gallery_container = None  # Store reference to gallery for refresh
        self.tabs = [
            {
                "title": "Home",
                "icon": ft.Icons.HOME,
                "content": self.generate_home_content,
            },
            {
                "title": "Images",
                "icon": ft.Icons.IMAGE,
                "content": self.generate_images_content,
            },
            {
                "title": "Settings",
                "icon": ft.Icons.SETTINGS,
                "content": self.generate_settings_content,
            },
        ]
    def generate_home_content(self):
        # Botón para limpiar tareas completadas

        # Crear contenido del home con botones y la cola de tareas
        home_content = ft.Column(
            [
                self.main_column,
            ],
            expand=True,
        )

        return home_content

    def generate_images_content(self):
        path_txt2img = os.path.join(app_data_path, "outputs", "txt2img")
        if os.path.exists(path_txt2img):
            images = [
                f for f in os.listdir(path_txt2img) if not f.lower().endswith(".json")
            ]
            images.sort(
                key=lambda x: os.path.getmtime(os.path.join(path_txt2img, x)),
                reverse=True,
            )
            image_widgets = []
            for image in images:
                image_path = os.path.join(path_txt2img, image)
                image_widget = ImageViewer(self.page, image_path, self.set_async_task).content
                image_widgets.append(image_widget)

            # Create and store gallery container for refresh capability
            self.gallery_container = ft.GridView(
                controls=image_widgets,
                expand=True,
                max_extent=200,
                spacing=10,
                run_spacing=10,
            )
            return self.gallery_container
        else:
            # Return empty container if no images directory
            self.gallery_container = ft.Container(
                content=ft.Text("No images found", text_align=ft.TextAlign.CENTER),
                expand=True,
                alignment=ft.alignment.center
            )
            return self.gallery_container

    def generate_settings_content(self):
        return ft.Text("Settings")

    def refresh_gallery(self):
        """Refresh the gallery with latest images"""
        if self.gallery_container is None:
            return

        path_txt2img = os.path.join(app_data_path, "outputs", "txt2img")
        if os.path.exists(path_txt2img):
            images = [
                f for f in os.listdir(path_txt2img) if not f.lower().endswith(".json")
            ]
            images.sort(
                key=lambda x: os.path.getmtime(os.path.join(path_txt2img, x)),
                reverse=True,
            )
            image_widgets = []
            for image in images:
                image_path = os.path.join(path_txt2img, image)
                image_widget = ImageViewer(self.page, image_path, self.set_async_task).content
                image_widgets.append(image_widget)

            # Update gallery container controls
            if hasattr(self.gallery_container, 'controls'):
                self.gallery_container.controls = image_widgets
                self.page.update()

    def generate_tab(self, tab_index):
        if tab_index != 0:
            # Remover ambos paneles cuando no estamos en Home
            self.hide_both_panels()
        else:
            # En Home, mostrar actions row por defecto
            self.show_actions_row()
        self.page.update()
        return self.tabs[tab_index]["content"]()

    async def try_save_value(self, key, value):
        await self.page.client_storage.set_async(key, value)

    async def on_navigation_bar_change(self, e):
        self.main_container.content = self.generate_tab(int(e.data))
        await self.try_save_value("selected_index", int(e.data))
        self.page.update()

    async def try_load_value(self, key, default_value=None):
        value = await self.page.client_storage.get_async(key)
        if value is None:
            await self.page.client_storage.set_async(key, default_value)
            return default_value
        await self.page.client_storage.set_async(key, value)
        return value

    async def set_async_task(self, label, payload, task_type="txt2img"):
        task = GenerationTaskTest(
            self.page, self.main_column, label, payload, self.task_queue, task_type, self
        )
        self.main_column.controls.insert(0, task.content)
        self.task_queue.add_task(task)
        self.page.update()

    async def floating_action(self, e):
        # Obtener payload completo del panel de prompts
        payload = self.prompt_panel.get_full_payload()

        # Crear título de la tarea
        task_title = (
            payload.prompt[:20] + "..." if len(payload.prompt) > 20 else payload.prompt
        )

        await self.set_async_task(task_title, payload)

    def clear_completed_tasks(self, e):
        """Limpiar solo las tareas completadas usando el sistema de datos"""
        tasks_to_remove = []

        for control in self.main_column.controls[:]:  # Crear copia
            # Verificar si el control tiene una referencia al objeto task
            if hasattr(control, "data") and control.data is not None:
                task = control.data
                # Verificar si la tarea está completada o tiene error
                if hasattr(task, "completed") and task.completed:
                    tasks_to_remove.append(control)
                elif hasattr(task, "has_error") and task.has_error:
                    tasks_to_remove.append(control)

        # Remover tareas completadas
        for task_control in tasks_to_remove:
            if task_control in self.main_column.controls:
                self.main_column.controls.remove(task_control)

        self.page.update()

    def get_task_statistics(self):
        """Obtener estadísticas de las tareas actuales"""
        stats = {
            "total": 0,
            "working": 0,
            "completed": 0,
            "error": 0,
            "queued": 0
        }

        for control in self.main_column.controls:
            if hasattr(control, "data") and control.data is not None:
                task = control.data
                stats["total"] += 1

                if hasattr(task, "working") and task.working:
                    stats["working"] += 1
                elif hasattr(task, "completed") and task.completed:
                    stats["completed"] += 1
                elif hasattr(task, "has_error") and task.has_error:
                    stats["error"] += 1
                else:
                    stats["queued"] += 1

        return stats
    
    def show_actions_row(self):
        """Agregar actions row al stack y remover prompt panel"""
        # Remover prompt panel si está presente
        if self.prompt_panel.content in self.main_stack.controls:
            self.main_stack.controls.remove(self.prompt_panel.content)

        # Agregar actions row si no está presente
        actions_container = ft.Container(
            content=self.main_actions_row,
            alignment=ft.alignment.bottom_right,
            padding=ft.padding.only(bottom=20, right=20),
        )
        if actions_container not in self.main_stack.controls:
            self.main_stack.controls.append(actions_container)

        self.main_stack.update()

    def show_prompt_panel(self):
        """Agregar prompt panel al stack y remover actions row"""
        # Remover actions row si está presente
        actions_containers = [c for c in self.main_stack.controls if hasattr(c, 'content') and c.content == self.main_actions_row]
        for container in actions_containers:
            self.main_stack.controls.remove(container)

        # Agregar prompt panel si no está presente
        if self.prompt_panel.content not in self.main_stack.controls:
            self.main_stack.controls.append(self.prompt_panel.content)

        self.prompt_panel.content.visible = True
        self.main_stack.update()

    def hide_both_panels(self):
        """Remover ambos paneles del stack"""
        # Remover prompt panel
        if self.prompt_panel.content in self.main_stack.controls:
            self.main_stack.controls.remove(self.prompt_panel.content)

        # Remover actions row
        actions_containers = [c for c in self.main_stack.controls if hasattr(c, 'content') and c.content == self.main_actions_row]
        for container in actions_containers:
            self.main_stack.controls.remove(container)

        self.main_stack.update()

    def toggle_prompt_panel(self, e):
        """Toggle entre prompt panel y actions row"""
        if self.prompt_panel.content in self.main_stack.controls:
            # Prompt panel está presente, cambiarlo por actions row
            self.show_actions_row()
        else:
            # Actions row está presente (o ninguno), mostrar prompt panel
            self.show_prompt_panel()

    async def main(self, page: ft.Page):
        self.page = page
        self.page.padding = 0
        page.title = "A1111 Client"

        # Crear directorio de salida para imágenes
        if app_data_path:
            output_dir = os.path.join(app_data_path, "outputs", "txt2img")
            os.makedirs(output_dir, exist_ok=True)

        self.selected_index = await self.try_load_value("selected_index", 0)

        self.navigation_bar = ft.NavigationBar(
            selected_index=self.selected_index,
            destinations=[
                ft.NavigationBarDestination(icon=ft.Icons.HOME, label="Home"),
                ft.NavigationBarDestination(icon=ft.Icons.IMAGE, label="Images"),
                ft.NavigationBarDestination(icon=ft.Icons.SETTINGS, label="Settings"),
            ],
            on_change=self.on_navigation_bar_change,
        )

        self.main_column = ft.Column(
            [],
            expand=True,
            scroll=ft.ScrollMode.AUTO,
            # max_extent=200,
            # spacing=10,
            # run_spacing=10,
        )
        self.main_container = ft.Container(self.main_column, expand=True)
        self.actions_generate = ft.IconButton(
            ft.Icons.ROCKET_LAUNCH,
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            on_click=self.floating_action,
            tooltip="Agregar tarea a la cola",
        )
        self.actions_clear_all = ft.IconButton(
            ft.Icons.CLEANING_SERVICES,
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            on_click=self.clear_completed_tasks,
            tooltip="Limpiar tareas completadas",
        )
        self.prompt_panel_toggle = ft.IconButton(
            ft.Icons.ARROW_DROP_DOWN,
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            on_click=self.toggle_prompt_panel,
            tooltip="Ocultar/mostrar panel de prompts",
        )
        
        self.prompt_panel = PromptPanel(self.page, self)
        self.main_actions_row = ft.Container(
            content=ft.Row(
                [
                    ft.Column(
                        [
                            self.prompt_panel_toggle,
                            self.actions_clear_all,
                            self.actions_generate,
                        ],
                        alignment=ft.MainAxisAlignment.END,
                    )
                ],
                alignment=ft.MainAxisAlignment.END,
            ),
            visible=True
        )
        self.main_stack = ft.Stack(
            expand=True,
            controls=[
                self.main_container,
                # Los paneles se agregan/remueven dinámicamente
            ],
        )
        self.main_area = ft.SafeArea(self.main_stack, expand=True)
        # self.floating_button = ft.FloatingActionButton(
        #     icon=ft.Icons.ADD,
        #     on_click=self.floating_action,
        #     tooltip="Agregar tarea a la cola",
        # )

        # self.page.floating_action_button = self.floating_button
        page.navigation_bar = self.navigation_bar
        page.add(self.main_area)
        self.main_container.content = self.generate_tab(self.selected_index)

        # Iniciar el procesador de cola
        self.page.run_task(self.task_queue.process_queue)

        # Cargar valores guardados del prompt panel
        self.page.run_task(self.prompt_panel.load_saved_values)

        page.update()