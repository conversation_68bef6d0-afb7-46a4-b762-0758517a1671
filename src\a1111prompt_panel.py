import flet as ft
import random
from a1111client import BasePayload

class PromptPanel:
    def __init__(self, page: ft.Page, app_instance):
        self.page = page
        self.app_instance = app_instance
        self.default_prompt_pressets = [
            "A professional photograph of an astronaut riding a horse",
            "A professional photograph of a cat riding a horse",
            "A professional photograph of a dog riding a horse",
            "A shot of a cave in the middle of the forest",
            "A shot of a cave in the middle of the forest at night with a full moon, a waterfall and a bridge",
            "Gemstones and jewelry",
            "A dinner table set for 8 people with a view of the mountains",
        ]
        self.content = self.build()

    def build(self):

        try_load_prompt = self.app_instance.try_load_value("prompt", "")
        try_load_negative_prompt = self.app_instance.try_load_value("negative_prompt", "")
        try_load_steps = self.app_instance.try_load_value("steps", 30)
        try_load_cfg_scale = self.app_instance.try_load_value("cfg_scale", 7)
        try_load_width = self.app_instance.try_load_value("width", 1024)
        try_load_height = self.app_instance.try_load_value("height", 1024)
        try_load_n_iter = self.app_instance.try_load_value("n_iter", 1)

        self.text_field_prompt = ft.TextField(
            value=try_load_prompt,
            label="Prompt",
            hint_text=random.choice(self.default_prompt_pressets),
            # helper_text="A professional photograph of an astronaut riding a horse",
            # expand=True,
        )
        self.text_field_negative_prompt = ft.TextField(
            value=try_load_negative_prompt,
            label="Negative Prompt",
            hint_text="blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            # helper_text="A professional photograph of an astronaut riding a horse",
            # expand=True,
        )
        self.slider_steps = ft.Slider(
            value=try_load_steps,
            min=10,
            max=100,
            divisions=9,
        )
        self.slider_cfg_scale = ft.Slider(
            value=try_load_cfg_scale,
            min=1,
            max=10,
            divisions=9,
        )
        # width and height 256  to 1536 32 by 32
        self.slider_width = ft.Slider(
            value=try_load_width,
            min=256,
            max=1536,
            divisions=47,
        )
        self.slider_height = ft.Slider(
            value=try_load_height,
            min=256,
            max=1536,
            divisions=47,
        )
        self.slider_n_iter = ft.Slider(
            value=try_load_n_iter,
            min=1,
            max=4,
            divisions=3,
        )

        self.prompt_column = ft.Column(
            [
                self.text_field_prompt,
                self.text_field_negative_prompt,
                ft.Row(
                    [
                        ft.Text("Steps"),
                        self.slider_steps,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("CFG Scale"),
                        self.slider_cfg_scale,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("Width"),
                        self.slider_width,
                    ],
                ),
                ft.Row(
                    [
                        ft.Text("Height"),
                        self.slider_height,
                    ],
                ),
                ft.Row(
                    [
                        ft.Text("N Iter"),
                        self.slider_n_iter,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
            ],
            expand=True,
        )
        return ft.Container(
            content=self.prompt_column,
            expand=True,
            offset=(2, 0),
            animate_offset=ft.Animation(300, ft.AnimationCurve.FAST_OUT_SLOWIN),
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            padding=10,
            border_radius=8,
        )


    def toggle(self):
        self.content.offset = (0, 0) if self.content.offset == (2, 0) else (2, 0)
        self.content.update()

    def close(self):
        self.content.offset = (2, 0)
        self.content.update()


    def get_full_payload(self):
        return BasePayload(
            prompt=self.text_field_prompt.value if self.text_field_prompt.value else self.text_field_prompt.hint_text,
            negative_prompt=self.text_field_negative_prompt.value,
            steps=int(self.slider_steps.value),
            cfg_scale=self.slider_cfg_scale.value,
            width=int(self.slider_width.value),
            height=int(self.slider_height.value),
            n_iter=int(self.slider_n_iter.value),
        )   
