import flet as ft
import random
from a1111client import BasePayload


class PromptPanel:
    def __init__(self, page: ft.Page, app_instance):
        self.page = page
        self.app_instance = app_instance
        self.default_prompt_pressets = [
            "A professional photograph of an astronaut riding a horse",
            "A professional photograph of a cat riding a horse",
            "A professional photograph of a dog riding a horse",
            "A shot of a cave in the middle of the forest",
            "A shot of a cave in the middle of the forest at night with a full moon, a waterfall and a bridge",
            "Gemstones and jewelry",
            "A dinner table set for 8 people with a view of the mountains",
        ]
        # Inicializar valores por defecto
        self.default_values = {
            "prompt": "",
            "negative_prompt": "",
            "steps": 30,
            "cfg_scale": 7.0,
            "width": 1024,
            "height": 1024,
            "n_iter": 1,
            "seed": -1,
            "hr_fix": False,
            "hr_scale": 2.0,
            "hr_second_steps": 15,
        }
        self.content = self.build()

    def build(self):
        # Usar valores por defecto inicialmente, luego cargar async
        self.current_values = self.default_values.copy()

        self.text_field_prompt = ft.TextField(
            value=self.current_values["prompt"],
            label="Prompt",
            hint_text=random.choice(self.default_prompt_pressets),
            on_change=self.on_prompt_change,
        )
        self.text_field_negative_prompt = ft.TextField(
            value=self.current_values["negative_prompt"],
            label="Negative Prompt",
            hint_text="blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
            on_change=self.on_negative_prompt_change,
        )

        # Seed field
        self.text_field_seed = ft.TextField(
            value=str(self.current_values["seed"]),
            label="Seed (-1 for random)",
            hint_text="-1",
            on_change=self.on_seed_change,
            width=150,
        )

        self.slider_steps = ft.Slider(
            value=self.current_values["steps"],
            min=10,
            max=100,
            divisions=9,
            on_change=self.on_steps_change,
        )
        self.slider_cfg_scale = ft.Slider(
            value=self.current_values["cfg_scale"],
            min=1,
            max=20,
            divisions=19,
            on_change=self.on_cfg_scale_change,
        )
        # width and height 256 to 1536 by 32
        self.slider_width = ft.Slider(
            value=self.current_values["width"],
            min=256,
            max=1536,
            divisions=40,  # (1536-256)/32 = 40
            on_change=self.on_width_change,
        )
        self.slider_height = ft.Slider(
            value=self.current_values["height"],
            min=256,
            max=1536,
            divisions=40,  # (1536-256)/32 = 40
            on_change=self.on_height_change,
        )
        self.slider_n_iter = ft.Slider(
            value=self.current_values["n_iter"],
            min=1,
            max=4,
            divisions=3,
            on_change=self.on_n_iter_change,
        )

        # HR Fix checkbox
        self.checkbox_hr_fix = ft.Checkbox(
            label="High Resolution Fix",
            value=self.current_values["hr_fix"],
            on_change=self.on_hr_fix_change,
        )

        # HR Fix parameters (initially hidden)
        self.slider_hr_scale = ft.Slider(
            value=self.current_values["hr_scale"],
            min=1.0,
            max=4.0,
            divisions=30,  # 0.1 increments
            on_change=self.on_hr_scale_change,
        )

        self.slider_hr_second_steps = ft.Slider(
            value=self.current_values["hr_second_steps"],
            min=5,
            max=50,
            divisions=9,  # 5-step increments
            on_change=self.on_hr_second_steps_change,
        )

        # HR Fix container (initially hidden)
        self.hr_fix_container = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("HR Scale"),
                            ft.Text(f"{self.current_values['hr_scale']:.1f}x"),
                            self.slider_hr_scale,
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                    ),
                    ft.Row(
                        [
                            ft.Text("HR Second Steps"),
                            ft.Text(f"{int(self.current_values['hr_second_steps'])}"),
                            self.slider_hr_second_steps,
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                    ),
                ]
            ),
            visible=self.current_values["hr_fix"],
            padding=ft.padding.only(left=20),
        )

        self.prompt_column = ft.Column(
            [
                self.text_field_prompt,
                self.text_field_negative_prompt,
                ft.Row(
                    [
                        self.text_field_seed,
                        ft.IconButton(
                            icon=ft.Icons.SHUFFLE,
                            tooltip="Random seed",
                            on_click=self.randomize_seed,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("Steps"),
                        ft.Text(f"{int(self.current_values['steps'])}"),
                        self.slider_steps,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("CFG Scale"),
                        ft.Text(f"{self.current_values['cfg_scale']:.1f}"),
                        self.slider_cfg_scale,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("Width"),
                        ft.Text(f"{int(self.current_values['width'])}"),
                        self.slider_width,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("Height"),
                        ft.Text(f"{int(self.current_values['height'])}"),
                        self.slider_height,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("N Iter"),
                        ft.Text(f"{int(self.current_values['n_iter'])}"),
                        self.slider_n_iter,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                self.checkbox_hr_fix,
                self.hr_fix_container,
            ],
            expand=True,
            scroll=ft.ScrollMode.AUTO,
        )
        # Botón de cierre en la esquina superior derecha
        self.close_button = ft.IconButton(
            icon=ft.Icons.CLOSE,
            tooltip="Cerrar panel",
            on_click=self.on_close_button_click,
            bgcolor=ft.Colors.ERROR_CONTAINER,
        )

        # Crear el contenido principal con el botón integrado
        main_content = ft.Container(
            content=ft.Column([
                # Header con botón de cierre
                ft.Row([
                    ft.Text("Prompt Settings", weight=ft.FontWeight.BOLD, color=ft.Colors.ON_PRIMARY_CONTAINER),
                    self.close_button,
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                # Contenido del panel
                ft.Container(
                    content=self.prompt_column,
                    expand=True,
                ),
            ], expand=True),
            expand=True,
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            padding=10,
            border_radius=8,
        )

        return ft.Container(
            content=main_content,
            expand=True,
            visible=False,
        )

    async def load_saved_values(self):
        """Cargar valores guardados de forma asíncrona"""
        try:
            for key, default_value in self.default_values.items():
                saved_value = await self.app_instance.try_load_value(key, default_value)
                self.current_values[key] = saved_value

            # Actualizar UI con valores cargados
            self.update_ui_values()
        except Exception as e:
            print(f"Error loading saved values: {e}")

    def update_ui_values(self):
        """Actualizar la UI con los valores actuales"""
        self.text_field_prompt.value = self.current_values["prompt"]
        self.text_field_negative_prompt.value = self.current_values["negative_prompt"]
        self.text_field_seed.value = str(self.current_values["seed"])
        self.slider_steps.value = self.current_values["steps"]
        self.slider_cfg_scale.value = self.current_values["cfg_scale"]
        self.slider_width.value = self.current_values["width"]
        self.slider_height.value = self.current_values["height"]
        self.slider_n_iter.value = self.current_values["n_iter"]
        self.checkbox_hr_fix.value = self.current_values["hr_fix"]
        self.slider_hr_scale.value = self.current_values["hr_scale"]
        self.slider_hr_second_steps.value = self.current_values["hr_second_steps"]
        self.hr_fix_container.visible = self.current_values["hr_fix"]

        # Actualizar labels de valores
        self.update_value_labels()
        self.page.update()

    def update_value_labels(self):
        """Actualizar los labels que muestran los valores actuales"""
        # Encontrar y actualizar los textos de valores en las filas
        for control in self.prompt_column.controls:
            if isinstance(control, ft.Row) and len(control.controls) >= 3:
                if isinstance(control.controls[0], ft.Text):
                    label = control.controls[0].value
                    if label == "Steps":
                        control.controls[
                            1
                        ].value = f"{int(self.current_values['steps'])}"
                    elif label == "CFG Scale":
                        control.controls[
                            1
                        ].value = f"{self.current_values['cfg_scale']:.1f}"
                    elif label == "Width":
                        control.controls[
                            1
                        ].value = f"{int(self.current_values['width'])}"
                    elif label == "Height":
                        control.controls[
                            1
                        ].value = f"{int(self.current_values['height'])}"
                    elif label == "N Iter":
                        control.controls[
                            1
                        ].value = f"{int(self.current_values['n_iter'])}"

        # Actualizar labels de HR Fix
        for control in self.hr_fix_container.content.controls:
            if isinstance(control, ft.Row) and len(control.controls) >= 3:
                if isinstance(control.controls[0], ft.Text):
                    label = control.controls[0].value
                    if label == "HR Scale":
                        control.controls[
                            1
                        ].value = f"{self.current_values['hr_scale']:.1f}x"
                    elif label == "HR Second Steps":
                        control.controls[
                            1
                        ].value = f"{int(self.current_values['hr_second_steps'])}"

    # Callback methods
    def on_prompt_change(self, e):
        self.current_values["prompt"] = e.control.value
        self.save_value("prompt", e.control.value)

    def on_negative_prompt_change(self, e):
        self.current_values["negative_prompt"] = e.control.value
        self.save_value("negative_prompt", e.control.value)

    def on_seed_change(self, e):
        try:
            seed_value = int(e.control.value) if e.control.value else -1
            self.current_values["seed"] = seed_value
            self.save_value("seed", seed_value)
        except ValueError:
            e.control.value = str(self.current_values["seed"])
            e.control.update()

    def on_steps_change(self, e):
        self.current_values["steps"] = int(e.control.value)
        self.update_value_labels()
        self.save_value("steps", int(e.control.value))

    def on_cfg_scale_change(self, e):
        self.current_values["cfg_scale"] = e.control.value
        self.update_value_labels()
        self.save_value("cfg_scale", e.control.value)

    def on_width_change(self, e):
        # Redondear a múltiplos de 32
        width = int(e.control.value)
        width = round(width / 32) * 32
        e.control.value = width
        self.current_values["width"] = width
        self.update_value_labels()
        self.save_value("width", width)

    def on_height_change(self, e):
        # Redondear a múltiplos de 32
        height = int(e.control.value)
        height = round(height / 32) * 32
        e.control.value = height
        self.current_values["height"] = height
        self.update_value_labels()
        self.save_value("height", height)

    def on_n_iter_change(self, e):
        self.current_values["n_iter"] = int(e.control.value)
        self.update_value_labels()
        self.save_value("n_iter", int(e.control.value))

    def on_hr_fix_change(self, e):
        self.current_values["hr_fix"] = e.control.value
        self.hr_fix_container.visible = e.control.value
        self.hr_fix_container.update()
        self.save_value("hr_fix", e.control.value)

    def on_hr_scale_change(self, e):
        self.current_values["hr_scale"] = e.control.value
        self.update_value_labels()
        self.save_value("hr_scale", e.control.value)

    def on_hr_second_steps_change(self, e):
        self.current_values["hr_second_steps"] = int(e.control.value)
        self.update_value_labels()
        self.save_value("hr_second_steps", int(e.control.value))

    def randomize_seed(self, e):
        import random

        new_seed = random.randint(0, 2147483647)
        self.current_values["seed"] = new_seed
        self.text_field_seed.value = str(new_seed)
        self.text_field_seed.update()
        self.save_value("seed", new_seed)

    def save_value(self, key, value):
        """Wrapper para guardar valor usando run_task correctamente"""

        async def save_task():
            try:
                await self.app_instance.try_save_value(key, value)
            except Exception as e:
                print(f"Error saving {key}: {e}")

        self.page.run_task(save_task)

    async def save_value_async(self, key, value):
        """Guardar valor de forma asíncrona (método directo)"""
        try:
            await self.app_instance.try_save_value(key, value)
        except Exception as e:
            print(f"Error saving {key}: {e}")

    def show(self):
        """Mostrar el panel con animación"""
        self.content.visible = True
        self.content.update()

    def close(self):
        """Cerrar el panel con animación"""
        self.content.visible = False
        self.content.update()

    def toggle(self):
        """Toggle del panel"""
        self.content.visible = not self.content.visible
        self.content.update()

    def on_close_button_click(self, e):
        """Callback del botón de cierre - notifica a la app principal"""
        if hasattr(self.app_instance, "show_actions_row"):
            self.close()
            self.app_instance.show_actions_row()

    def get_full_payload(self):
        payload_data = {
            "prompt": self.text_field_prompt.value
            if self.text_field_prompt.value
            else self.text_field_prompt.hint_text,
            "negative_prompt": self.text_field_negative_prompt.value,
            "steps": int(self.slider_steps.value),
            "cfg_scale": self.slider_cfg_scale.value,
            "width": int(self.slider_width.value),
            "height": int(self.slider_height.value),
            "n_iter": int(self.slider_n_iter.value),
            "seed": int(self.text_field_seed.value)
            if self.text_field_seed.value
            else -1,
            "sampler_name": "Euler a",  # Valor por defecto
            "scheduler": "Normal",  # Valor por defecto
        }

        # Agregar parámetros de HR Fix si está habilitado
        if self.checkbox_hr_fix.value:
            payload_data.update(
                {
                    "enable_hr": True,
                    "hr_scale": self.slider_hr_scale.value,
                    "hr_second_pass_steps": int(self.slider_hr_second_steps.value),
                    "hr_upscaler": "R-ESRGAN 4x+",  # Valor por defecto
                    "denoising_strength": 0.5,  # Valor por defecto
                    "hr_resize_x": int(
                        self.slider_width.value * self.slider_hr_scale.value
                    ),
                    "hr_resize_y": int(
                        self.slider_height.value * self.slider_hr_scale.value
                    ),
                }
            )

        return BasePayload(**payload_data)
