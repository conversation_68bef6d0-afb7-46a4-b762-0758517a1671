import flet as ft
import os
import json


app_data_path = os.getenv("FLET_APP_STORAGE_DATA")
app_temp_path = os.getenv("FLET_APP_STORAGE_TEMP")

class ImageViewer:

    def load_image_json(self):
        """Load JSON metadata with robust error handling and automatic cleanup"""
        json_path = self.image_path.replace(".png", ".json")

        # Return None if JSON file doesn't exist
        if not os.path.exists(json_path):
            return None

        try:
            # Check if file is empty before attempting to parse
            if os.path.getsize(json_path) == 0:
                self._cleanup_corrupted_files(json_path)
                return None

            # Attempt to read and parse JSON
            with open(json_path, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # Check if content is empty or whitespace only
                if not content:
                    self._cleanup_corrupted_files(json_path)
                    return None

                # Parse JSON content
                json_data = json.loads(content)

                # Validate that we have a dictionary with expected structure
                if not isinstance(json_data, dict):
                    self._cleanup_corrupted_files(json_path)
                    return None

                # Basic validation - ensure we have at least some expected keys
                if not any(key in json_data for key in ["parameters", "info", "image_filename"]):
                    self._cleanup_corrupted_files(json_path)
                    return None

                return json_data

        except (json.JSONDecodeError, ValueError):
            # JSON parsing failed - corrupted JSON
            self._cleanup_corrupted_files(json_path)
            return None

        except (FileNotFoundError, PermissionError, OSError, IOError):
            # File system errors - file might be locked, deleted, or permission issues
            return None

        except UnicodeDecodeError:
            # Encoding issues
            self._cleanup_corrupted_files(json_path)
            return None

        except Exception:
            # Catch any other unexpected errors to prevent app crashes
            self._cleanup_corrupted_files(json_path)
            return None

    def _cleanup_corrupted_files(self, json_path):
        """Safely delete corrupted JSON and corresponding PNG files"""
        try:
            # Delete the corrupted JSON file
            if os.path.exists(json_path):
                os.remove(json_path)

            # Delete the corresponding PNG file
            png_path = json_path.replace(".json", ".png")
            if os.path.exists(png_path):
                os.remove(png_path)

        except (PermissionError, OSError, FileNotFoundError):
            # If we can't delete files, silently continue
            # This prevents secondary errors from breaking the app
            pass
        except Exception:
            # Catch any other unexpected errors during cleanup
            pass

    def __init__(self, page: ft.Page, image_path, generate_callback):
        self.page = page
        self.image_path = image_path
        self.generate_callback = generate_callback
        self.image_json = self.load_image_json()
        self.content = self.build()


    def upscale(self, e):
        """Crear payload para upscale con HR (High Resolution)"""
        from a1111client import BasePayload

        # Check if JSON data is available
        if not self.image_json:
            # Create default upscale payload if no JSON metadata
            upscale_payload = BasePayload(
                prompt="upscaled image",
                n_iter=1,
                enable_hr=True,
                hr_upscaler="R-ESRGAN 4x+",
                hr_second_pass_steps=10,
                hr_scale=1.5,
                denoising_strength=0.7,
                width=512,
                height=512
            )
            prompt_preview = "upscaled"
        else:
            # Obtener parámetros originales
            params = self.image_json.get("parameters", {})

            # Crear payload base con parámetros originales
            base_payload = BasePayload.from_dict(params)

            # Crear payload para upscale con parámetros HR
            upscale_payload = base_payload.copy_with_updates(
                n_iter=1,
                enable_hr=True,
                hr_upscaler="R-ESRGAN 4x+",
                hr_second_pass_steps=10,
                hr_scale=1.5,
                denoising_strength=0.7,  # Para HR
                # Mantener dimensiones originales para primera pasada
                width=params.get("width", 512),
                height=params.get("height", 512)
            )

            prompt_preview = params.get("prompt", "")[:20]

        async def upscale_task():
            await self.generate_callback(f"Upscale: {prompt_preview}", upscale_payload)

        self.page.run_task(upscale_task)

    def repeat(self, e):
        """Repetir con los mismos parámetros originales"""
        from a1111client import BasePayload

        # Check if JSON data is available
        if not self.image_json:
            # Create default payload if no JSON metadata
            payload = BasePayload(
                prompt="repeated image",
                n_iter=1,
                width=512,
                height=512
            )
            prompt_preview = "repeated"
        else:
            # Crear payload exactamente igual al original
            params = self.image_json.get("parameters", {})
            payload = BasePayload.from_dict(params)
            prompt_preview = params.get("prompt", "")[:20]

        async def repeat_task():
            await self.generate_callback(f"Repeat: {prompt_preview}", payload)

        self.page.run_task(repeat_task)


    def get_alert_dialog(self):
        self.button_upscale = ft.ElevatedButton(
            "Upscale",
            on_click=self.upscale,
        )
        self.button_repeat = ft.ElevatedButton(
            "Repeat",
            on_click=self.repeat,
        )

        # Create content based on whether JSON data is available
        if self.image_json:
            params = self.image_json.get('parameters', {})
            content_controls = [
                ft.Text(f"Image: {os.path.basename(self.image_path)}"),
                ft.Text(f"Prompt: {params.get('prompt', 'N/A')}"),
                ft.Text(f"Negative Prompt: {params.get('negative_prompt', 'N/A')}"),
                ft.Text(f"Steps: {params.get('steps', 'N/A')}"),
                ft.Text(f"CFG Scale: {params.get('cfg_scale', 'N/A')}"),
                ft.Text(f"Sampler: {params.get('sampler_name', 'N/A')}"),
                ft.Text(f"Scheduler: {params.get('scheduler', 'N/A')}"),
                self.button_upscale,
                self.button_repeat,
            ]
        else:
            content_controls = [
                ft.Text(f"Image: {os.path.basename(self.image_path)}"),
                ft.Text("⚠️ No metadata available", color=ft.Colors.ORANGE),
                ft.Text("JSON file was missing or corrupted"),
                ft.Text("Basic operations still available:"),
                self.button_upscale,
                self.button_repeat,
            ]

        self.dialog = ft.AlertDialog(
            title=ft.Text("Image Info"),
            content=ft.Column(
                content_controls,
                spacing=8,
            ),
            actions=[
                ft.TextButton("OK", on_click=lambda e: self.page.close(self.dialog)),
            ],
        )
        return self.dialog

    def open_dialog(self, e):
        self.page.open(self.get_alert_dialog())

    def build(self):
        return ft.Container(
            content=ft.Image(
                src=self.image_path,
                width=200,
                height=200,
                fit=ft.ImageFit.CONTAIN,
                border_radius=ft.border_radius.all(8),
            ),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            margin=ft.margin.only(bottom=5),
            on_click=self.open_dialog,
        )

