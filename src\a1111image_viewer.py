import flet as ft
import os
import json


app_data_path = os.getenv("FLET_APP_STORAGE_DATA")
app_temp_path = os.getenv("FLET_APP_STORAGE_TEMP")

class ImageViewer:

    def load_image_json(self):
        json_path = self.image_path.replace(".png", ".json")
        if os.path.exists(json_path):
            with open(json_path, "r", encoding="utf-8") as f:
                return json.load(f)
        return None

    def __init__(self, page: ft.Page, image_path, generate_callback):
        self.page = page
        self.image_path = image_path
        self.generate_callback = generate_callback
        self.image_json = self.load_image_json()
        self.content = self.build()


    def upscale(self, e):
        """Crear payload para upscale con HR (High Resolution)"""
        from a1111client import BasePayload

        # Obtener parámetros originales
        params = self.image_json.get("parameters", {})

        # Crear payload base con parámetros originales
        base_payload = BasePayload.from_dict(params)

        # Crear payload para upscale con parámetros HR
        upscale_payload = base_payload.copy_with_updates(
            n_iter=1,
            enable_hr=True,
            hr_upscaler="R-ESRGAN 4x+",
            hr_second_pass_steps=10,
            hr_scale=1.5,
            denoising_strength=0.7,  # Para HR
            # Mantener dimensiones originales para primera pasada
            width=params.get("width", 512),
            height=params.get("height", 512)
        )

        prompt_preview = params.get("prompt", "")[:20]

        async def upscale_task():
            await self.generate_callback(f"Upscale: {prompt_preview}", upscale_payload)

        self.page.run_task(upscale_task)

    def repeat(self, e):
        """Repetir con los mismos parámetros originales"""
        from a1111client import BasePayload

        # Crear payload exactamente igual al original
        params = self.image_json.get("parameters", {})
        payload = BasePayload.from_dict(params)

        prompt_preview = params.get("prompt", "")[:20]

        async def repeat_task():
            await self.generate_callback(f"Repeat: {prompt_preview}", payload)

        self.page.run_task(repeat_task)


    def get_alert_dialog(self):
        self.button_upscale = ft.ElevatedButton(
            "Upscale",
            on_click=self.upscale,
        )
        self.button_repeat = ft.ElevatedButton(
            "Repeat",
            on_click=self.repeat,
        )
        self.dialog = ft.AlertDialog(
            title=ft.Text("Image Info"),
            content=ft.Column(
                [
                    ft.Text(f"Image: {self.image_path}"),
                    ft.Text(f"Prompt: {self.image_json.get('parameters', {}).get('prompt', '')}"),
                    ft.Text(f"Negative Prompt: {self.image_json.get('parameters', {}).get('negative_prompt', '')}"),
                    ft.Text(f"Steps: {self.image_json.get('parameters', {}).get('steps', '')}"),
                    ft.Text(f"CFG Scale: {self.image_json.get('parameters', {}).get('cfg_scale', '')}"),
                    ft.Text(f"Sampler: {self.image_json.get('parameters', {}).get('sampler_name', '')}"),
                    ft.Text(f"Scheduler: {self.image_json.get('parameters', {}).get('scheduler', '')}"),
                    self.button_upscale,
                    self.button_repeat,
                ],
                spacing=8,
            ),
            actions=[
                ft.TextButton("OK", on_click=lambda e: self.page.close(self.dialog)),
            ],
        )
        return self.dialog

    def open_dialog(self, e):
        self.page.open(self.get_alert_dialog())

    def build(self):
        return ft.Container(
            content=ft.Image(
                src=self.image_path,
                width=200,
                height=200,
                fit=ft.ImageFit.CONTAIN,
                border_radius=ft.border_radius.all(8),
            ),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            margin=ft.margin.only(bottom=5),
            on_click=self.open_dialog,
        )

